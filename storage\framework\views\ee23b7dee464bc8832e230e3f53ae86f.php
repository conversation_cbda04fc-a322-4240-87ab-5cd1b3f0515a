<script src="https://cdn.jsdelivr.net/npm/marked/marked.min.js"></script>

<!-- Enhanced Chatbot Widget -->
<div id="chatbot-widget" class="chatbot-widget">
    <!-- Toggle Button -->
    <button id="chatbot-toggle" class="chatbot-toggle" title="Chat with LocaBot">
        <i class="fas fa-comments chatbot-icon"></i>
        <span class="chatbot-notification-badge" id="chatbot-badge" style="display: none;">1</span>
    </button>

    <!-- Chat Box -->
    <div id="chatbot-box" class="chatbot-box" style="display: none;">
        <div class="chatbot-container">
            <!-- Header -->
            <div class="chatbot-header">
                <div class="chatbot-header-info">
                    <div class="chatbot-avatar">
                        <i class="fas fa-robot"></i>
                    </div>
                    <div class="chatbot-title">
                        <strong>LocaBot</strong>
                        <small class="chatbot-status">
                            <span class="status-indicator"></span>
                            Online
                        </small>
                    </div>
                </div>
                <div class="chatbot-header-actions">
                    <button class="chatbot-action-btn clear-chat" id="clear-chat" title="Clear conversation">
                        <i class="fas fa-trash-alt clear-chat"></i>
                    </button>
                    <button class="chatbot-action-btn" id="minimize-chat" title="Minimize">
                        <i class="fas fa-minus"></i>
                    </button>
                    
                </div>
            </div>

            <!-- Messages Container -->
            <div class="chatbot-messages" id="chatbot-messages">
                <div class="chatbot-welcome-message">
                    <div class="message bot-message">
                        <div class="message-avatar">
                            <i class="fas fa-robot"></i>
                        </div>
                        <div class="message-content">
                            <div class="message-bubble">
                                👋 Hello! I'm LocaBot, your guide to LocaSpace. I can help you:
                                <ul class="welcome-features">
                                    <li>🏢 Find available spaces</li>
                                    <li>📅 Make reservations</li>
                                    <li>🧭 Navigate the website</li>
                                    <li>❓ Answer your questions</li>
                                </ul>
                                How can I assist you today?
                            </div>
                            <div class="message-time">Just now</div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Typing Indicator -->
            <div class="typing-indicator" id="typing-indicator" style="display: none;">
                <div class="message bot-message">
                    <div class="message-avatar">
                        <i class="fas fa-robot"></i>
                    </div>
                    <div class="message-content">
                        <div class="typing-dots">
                            <span></span>
                            <span></span>
                            <span></span>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Quick Actions -->
            <div class="chatbot-quick-actions" id="quick-actions">
                <button class="quick-action-btn" data-message="Show me available spaces">
                    🏢 Available Spaces
                </button>
                <button class="quick-action-btn" data-message="How do I make a reservation?">
                    📅 How to Book
                </button>
                <button class="quick-action-btn" data-message="Help me navigate the website">
                    🧭 Website Guide
                </button>
            </div>

            <!-- Input Area -->
            <div class="chatbot-input-area">
                <div class="input-container">
                    <textarea id="chatbot-input" class="chatbot-input" placeholder="Type your message..." rows="1"
                        maxlength="1000"></textarea>
                    <button id="send-message" class="send-btn" disabled>
                        <i class="fas fa-paper-plane"></i>
                    </button>
                </div>
                <div class="input-footer">
                    <small class="text-muted">
                        <span id="char-count">0</span>/1000 characters
                    </small>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Immediate Script for Chatbot Toggle -->
<p id="chat-conversation-id"><?php
        echo session()->get('chat')
    ?></p>
<script>
    let conversation_id = +(document.querySelector("#chat-conversation-id").textContent.replace("\n", "").trim()) || null;
    console.log(conversation_id, document.querySelector("#chat-conversation-id").textContent.replace("\n", "").trim())
    // Define the toggle function immediately so it's available for any onclick handlers
    function toggleChatbotSimple() {
        console.log('🧪 Simple toggle function called!');
        const box = document.getElementById('chatbot-box');
        if (box) {
            if (box.style.display != "block") {
                box.style.display = 'block';
                console.log('📖 Chatbot opened via simple function');
            } else {
                box.style.display = 'none';
                console.log('📕 Chatbot closed via simple function');
            }
        } else {
            console.error('❌ Chatbot box not found in simple function');
        }
    }

    // Make it available globally
    window.toggleChatbotSimple = toggleChatbotSimple;

    // Add click handler immediately when DOM is ready
    document.addEventListener('DOMContentLoaded', function () {
        console.log('🔧 Adding immediate click handler...');
        const ChatBotIconToggel = document.getElementById('chatbot-toggle');
        if (ChatBotIconToggel) {
            ChatBotIconToggel.addEventListener('click', function (e) {
                e.preventDefault();
                console.log('🖱️ Chatbot button clicked!');
                toggleChatbotSimple();
            });
            console.log('✅ Click handler added successfully!');
        } else {
            console.error('❌ Chatbot toggle button not found!');
        }
        const minimizeChatToggel = document.getElementById("minimize-chat")
        if (minimizeChatToggel) {
            minimizeChatToggel.addEventListener('click', function (e) {
                e.preventDefault();
                console.log('🖱️ Chatbot button clicked!');
                toggleChatbotSimple();
            });
            console.log('✅ Click handler added successfully!');
        }


        setupMessageHandlers();
        document.querySelectorAll(".clear-chat").forEach(e => { e.onclick = (e) => { deleteConversation(); }; console.log("ref") });
    });

    function setupMessageHandlers() {
        console.log('🔧 Setting up message handlers...');

        const input = document.getElementById('chatbot-input');
        const sendBtn = document.getElementById('send-message');
        const charCount = document.getElementById('char-count');

        if (!input || !sendBtn) {
            console.error('❌ Input elements not found!');
            return;
        }

        // Input change handler
        input.addEventListener('input', function () {
            const length = input.value.length;
            if (charCount) charCount.textContent = length;
            sendBtn.disabled = length === 0 || length > 1000;

            // Auto-resize textarea
            input.style.height = 'auto';
            input.style.height = Math.min(input.scrollHeight, 100) + 'px';

            console.log('📝 Input changed, length:', length);
        });

        // Enter key handler
        input.addEventListener('keydown', function (e) {
            if (e.key === 'Enter' && !e.shiftKey) {
                e.preventDefault();
                sendMessage();
            }
        });

        // Send button handler
        sendBtn.addEventListener('click', function (e) {
            e.preventDefault();
            sendMessage();
        });

        // Quick action handlers
        const quickActions = document.getElementById('quick-actions');
        if (quickActions) {
            quickActions.addEventListener('click', function (e) {
                if (e.target.classList.contains('quick-action-btn')) {
                    const message = e.target.dataset.message;
                    sendMessage(message);
                }
            });
        }

        console.log('✅ Message handlers set up successfully!');
    }

    function sendMessage(message = null) {
        console.log('📤 Sending message...');

        const input = document.getElementById('chatbot-input');
        const text = message || input.value.trim();

        if (!text) {
            console.log('❌ No message to send');
            return;
        }

        console.log('📤 Message text:', text);

        // Clear input if not using quick action
        if (!message && input) {
            input.value = '';
            input.style.height = 'auto';
            const charCount = document.getElementById('char-count');
            if (charCount) charCount.textContent = '0';
            const sendBtn = document.getElementById('send-message');
            if (sendBtn) sendBtn.disabled = true;
        }

        // Add user message to UI
        addMessageToChat(text, 'user');
        showTypingIndicator();

        // Send to API
        sendToAPI(text);
    }

    function addMessageToChat(text, sender, isError = false) {
        console.log('💬 Adding message to chat:', sender, text);

        const messages = document.getElementById('chatbot-messages');
        if (!messages) {
            console.error('❌ Messages container not found!');
            return;
        }

        const messageDiv = document.createElement('div');
        messageDiv.className = `message ${sender}-message`;

        const time = new Date().toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' });

        messageDiv.innerHTML = `
        <div class="message-avatar">
            <i class="fas fa-${sender === 'user' ? 'user' : 'robot'}"></i>
        </div>
        <div class="message-content">
            <div class="message-bubble ${isError ? 'error-message' : ''}">
                ${formatMessage(text)}
            </div>
            <div class="message-time">${time}</div>
        </div>
    `;

        // Remove welcome message if this is the first real conversation
        const welcomeMessage = messages.querySelector('.chatbot-welcome-message');


        messages.appendChild(messageDiv);
        scrollToBottom();
    }

    function formatMessage(text) {

        const html = marked.parse(text);
        return html;
    }

    function showTypingIndicator() {
        const indicator = document.getElementById('typing-indicator');
        if (indicator) {
            indicator.style.display = 'block';
            scrollToBottom();
        }
    }

    function hideTypingIndicator() {
        const indicator = document.getElementById('typing-indicator');
        if (indicator) {
            indicator.style.display = 'none';
        }
    }

    function scrollToBottom() {
        const messages = document.getElementById('chatbot-messages');
        if (messages) {
            setTimeout(() => {
                messages.scrollTop = messages.scrollHeight;
            }, 100);
        }
    }

    async function sendToAPI(text) {
        console.log('🌐 Sending to API:', text);

        try {
            const csrfToken = document.querySelector('meta[name="csrf-token"]');
            if (!csrfToken) {
                throw new Error('CSRF token not found');
            }
            console.log(conversation_id)

            const response = await fetch('/api/chatbot/chat', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'X-CSRF-TOKEN': csrfToken.getAttribute('content')
                },
                body: JSON.stringify({
                    message: text,
                    conversation_id: conversation_id
                })
            });

            const data = await response.json();
            conversation_id = data.conversation_id;
            hideTypingIndicator();

            if (response.ok) {
                console.log('✅ API response received:', data.reply);
                addMessageToChat(data.reply, 'bot');
            } else {
                console.error('❌ API error:', data.error);
                addMessageToChat(data.error || 'Sorry, I encountered an error. Please try again.', 'bot', true);
            }
        } catch (error) {
            console.error('❌ Network error:', error);
            hideTypingIndicator();
            addMessageToChat('Sorry, I\'m having trouble connecting. Please check your internet connection and try again.', 'bot', true);
        }
    }
    async function deleteConversation() {
        console.log('Deleting conversation...', conversation_id);
        if (!conversation_id) return;
        try {
            const response = await fetch('/api/chatbot/conversations/' + conversation_id, {
                method: 'DELETE',
                headers: {
                    'Content-Type': 'application/json',
                    'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
                }
            });

            const data = await response.json();

            if (response.ok) {
                console.log('✅ Conversation deleted:', data.message);
                location.reload();
            } else {
                console.error('❌ Error deleting conversation:', data.error);
            }
        } catch (error) {
            console.error('❌ Network error:', error);
        }
    }
</script>

<!--handle hystory of chat-->
<p id="history">
    <?php
        echo json_encode(\App\Http\Controllers\ChatbotController::getHistory());// conv data here 
    ?>
</p>

<script>
    const history = JSON.parse(document.getElementById('history').innerHTML).original;
    document.getElementById('history').remove();
    history.forEach((msg, index) => {
        addMessageToChat(msg.content, msg.role, msg.error ?? false);
    })
</script>
<!-- Chatbot Styles -->
<style>
    .chatbot-widget {
        position: fixed;
        bottom: 24px;
        right: 24px;
        z-index: 1000;
        font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    }

    .chatbot-toggle {
        width: 60px;
        height: 60px;
        border-radius: 50%;
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        border: none;
        color: white;
        font-size: 24px;
        cursor: pointer;
        box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
        transition: all 0.3s ease;
        position: relative;
        display: flex;
        align-items: center;
        justify-content: center;
        z-index: 1001;
        pointer-events: auto;
    }

    .chatbot-toggle:hover {
        transform: scale(1.1);
        box-shadow: 0 6px 25px rgba(0, 0, 0, 0.2);
    }

    .chatbot-notification-badge {
        position: absolute;
        top: -5px;
        right: -5px;
        background: #ff4757;
        color: white;
        border-radius: 50%;
        width: 20px;
        height: 20px;
        font-size: 12px;
        display: flex;
        align-items: center;
        justify-content: center;
        font-weight: bold;
    }

    .chatbot-box {
        position: absolute;
        bottom: 75px;
        right: 0;
        width: 380px;
        height: 550px;
        background: white;
        border-radius: 16px;
        box-shadow: 0 10px 40px rgba(0, 0, 0, 0.15);
        overflow: hidden;
        animation: slideUp 0.3s ease-out;
    }

    @keyframes slideUp {
        from {
            opacity: 0;
            transform: translateY(20px);
        }

        to {
            opacity: 1;
            transform: translateY(0);
        }
    }

    .chatbot-container {
        display: flex;
        flex-direction: column;
        height: 100%;
    }

    .chatbot-header {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
        padding: 16px;
        display: flex;
        justify-content: space-between;
        align-items: center;
    }

    .chatbot-header-info {
        display: flex;
        align-items: center;
        gap: 12px;
    }

    .chatbot-avatar {
        width: 40px;
        height: 40px;
        background: rgba(255, 255, 255, 0.2);
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 18px;
    }

    .chatbot-title strong {
        display: block;
        font-size: 16px;
    }

    .chatbot-status {
        display: flex;
        align-items: center;
        gap: 6px;
        font-size: 12px;
        opacity: 0.9;
    }

    .status-indicator {
        width: 8px;
        height: 8px;
        background: #2ed573;
        border-radius: 50%;
        animation: pulse 2s infinite;
    }

    @keyframes pulse {
        0% {
            opacity: 1;
        }

        50% {
            opacity: 0.5;
        }

        100% {
            opacity: 1;
        }
    }

    .chatbot-header-actions {
        display: flex;
        gap: 8px;
    }

    .chatbot-action-btn {
        background: rgba(255, 255, 255, 0.2);
        border: none;
        color: white;
        width: 32px;
        height: 32px;
        border-radius: 6px;
        cursor: pointer;
        transition: background 0.2s;
    }

    .chatbot-action-btn:hover {
        background: rgba(255, 255, 255, 0.3);
    }

    .chatbot-messages {
        flex: 1;
        overflow-y: auto;
        padding: 16px;
        background: #f8f9fa;
    }

    .message {
        display: flex;
        gap: 12px;
        margin-bottom: 16px;
    }

    .message.user-message {
        flex-direction: row-reverse;
    }

    .message-avatar {
        width: 32px;
        height: 32px;
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 14px;
        flex-shrink: 0;
    }

    .bot-message .message-avatar {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
    }

    .user-message .message-avatar {
        background: #007bff;
        color: white;
    }

    .message-content {
        max-width: 70%;
    }

    .message-bubble {
        background: white;
        padding: 12px 16px;
        border-radius: 18px;
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
        word-wrap: break-word;
    }

    .user-message .message-bubble {
        background: #007bff;
        color: white;
    }

    .message-time {
        font-size: 11px;
        color: #6c757d;
        margin-top: 4px;
        text-align: right;
    }

    .user-message .message-time {
        text-align: left;
    }

    .welcome-features {
        margin: 8px 0;
        padding-left: 16px;
    }

    .welcome-features li {
        margin: 4px 0;
    }

    .typing-dots {
        display: flex;
        gap: 4px;
        padding: 12px 16px;
    }

    .typing-dots span {
        width: 8px;
        height: 8px;
        background: #6c757d;
        border-radius: 50%;
        animation: typing 1.4s infinite;
    }

    .typing-dots span:nth-child(2) {
        animation-delay: 0.2s;
    }

    .typing-dots span:nth-child(3) {
        animation-delay: 0.4s;
    }

    @keyframes typing {

        0%,
        60%,
        100% {
            transform: translateY(0);
        }

        30% {
            transform: translateY(-10px);
        }
    }

    .chatbot-quick-actions {
        padding: 12px 16px;
        background: white;
        border-top: 1px solid #e9ecef;
        display: flex;
        gap: 8px;
        flex-wrap: wrap;
    }

    .quick-action-btn {
        background: #f8f9fa;
        border: 1px solid #dee2e6;
        border-radius: 20px;
        padding: 6px 12px;
        font-size: 12px;
        cursor: pointer;
        transition: all 0.2s;
        white-space: nowrap;
    }

    .quick-action-btn:hover {
        background: #e9ecef;
        transform: translateY(-1px);
    }

    .chatbot-input-area {
        background: white;
        border-top: 1px solid #e9ecef;
        padding: 16px;
    }

    .input-container {
        display: flex;
        gap: 12px;
        align-items: flex-end;
    }

    .chatbot-input {
        flex: 1;
        border: 1px solid #dee2e6;
        border-radius: 20px;
        padding: 12px 16px;
        resize: none;
        outline: none;
        font-family: inherit;
        font-size: 14px;
        max-height: 100px;
        transition: border-color 0.2s;
    }

    .chatbot-input:focus {
        border-color: #667eea;
    }

    .send-btn {
        width: 40px;
        height: 40px;
        border-radius: 50%;
        background: #667eea;
        border: none;
        color: white;
        cursor: pointer;
        transition: all 0.2s;
        display: flex;
        align-items: center;
        justify-content: center;
    }

    .send-btn:disabled {
        background: #6c757d;
        cursor: not-allowed;
    }

    .send-btn:not(:disabled):hover {
        background: #5a6fd8;
        transform: scale(1.1);
    }

    .input-footer {
        margin-top: 8px;
        text-align: right;
    }

    /* Mobile Responsive */
    @media (max-width: 480px) {
        .chatbot-box {
            width: calc(100vw - 32px);
            height: calc(100vh - 100px);
            right: 16px;
            bottom: 90px;
        }

        .chatbot-widget {
            right: 16px;
            bottom: 16px;
        }
    }

    /* Scrollbar Styling */
    .chatbot-messages::-webkit-scrollbar {
        width: 6px;
    }

    .chatbot-messages::-webkit-scrollbar-track {
        background: #f1f1f1;
    }

    .chatbot-messages::-webkit-scrollbar-thumb {
        background: #c1c1c1;
        border-radius: 3px;
    }

    .chatbot-messages::-webkit-scrollbar-thumb:hover {
        background: #a8a8a8;
    }

    /* Error message styling */
    .error-message {
        background: #f8d7da !important;
        color: #721c24 !important;
        border: 1px solid #f5c6cb !important;
    }
</style>

<?php $__env->startPush('scripts'); ?>
    <script>
        // Define the function immediately so it's available for onclick
        function toggleChatbotSimple() {
            console.log('🧪 Simple toggle function called!');
            const box = document.getElementById('chatbot-box');
            if (box) {
                if (box.style.display === 'none' || box.style.display === '') {
                    box.style.display = 'block';
                    console.log('📖 Chatbot opened via simple function');
                } else {
                    box.style.display = 'none';
                    console.log('📕 Chatbot closed via simple function');
                }
            } else {
                console.error('❌ Chatbot box not found in simple function');
            }
        }

        // Also make it available on window object
        window.toggleChatbotSimple = toggleChatbotSimple;

        // Simple test function
        window.testChatbotToggle = function () {
            console.log('🧪 Test function called!');
            const box = document.getElementById('chatbot-box');
            if (box) {
                if (box.style.display === 'none' || box.style.display === '') {
                    box.style.display = 'block';
                    console.log('📖 Chatbot opened via test function');
                } else {
                    box.style.display = 'none';
                    console.log('📕 Chatbot closed via test function');
                }
            } else {
                console.error('❌ Chatbot box not found in test function');
            }
        };

        // Emergency fallback - simple click handler
        document.addEventListener('DOMContentLoaded', function () {
            console.log('🚨 Emergency fallback loading...');

            setTimeout(function () {
                const toggle = document.getElementById('chatbot-toggle');
                const box = document.getElementById('chatbot-box');

                if (toggle && box) {
                    console.log('🔧 Adding emergency click handler...');

                    // Remove any existing event listeners and add a simple one
                    toggle.onclick = function (e) {
                        e.preventDefault();
                        console.log('🖱️ Emergency click handler triggered!');

                        if (box.style.display === 'none' || box.style.display === '') {
                            box.style.display = 'block';
                            console.log('📖 Chatbot opened (emergency)');
                        } else {
                            box.style.display = 'none';
                            console.log('📕 Chatbot closed (emergency)');
                        }
                    };

                    console.log('✅ Emergency handler attached!');
                } else {
                    console.error('❌ Emergency fallback failed - elements not found');
                }
            }, 1000);
        });

        // Initialize chatbot when DOM is ready
        document.addEventListener('DOMContentLoaded', function () {
            console.log('🔍 Initializing LocaChatbot...');

            // Check if elements exist
            const toggle = document.getElementById('chatbot-toggle');
            const box = document.getElementById('chatbot-box');

            if (!toggle) {
                console.error('❌ Chatbot toggle button not found!');
                return;
            }

            if (!box) {
                console.error('❌ Chatbot box not found!');
                return;
            }

            console.log('✅ Chatbot elements found, initializing...');

            try {
                console.log('✅ LocaChatbot initialized successfully!');
            } catch (error) {
                console.error('❌ Error initializing LocaChatbot:', error);

                // Fallback: Add simple click handler
                console.log('🔧 Adding fallback click handler...');
                toggle.addEventListener('click', function () {
                    console.log('🖱️ Chatbot toggle clicked (fallback)');
                    if (box.style.display === 'none' || box.style.display === '') {
                        box.style.display = 'block';
                        console.log('📖 Chatbot opened');
                    } else {
                        box.style.display = 'none';
                        console.log('📕 Chatbot closed');
                    }
                });
            }
        });

        // Add error message styling
        const style = document.createElement('style');
        style.textContent = `
                                                                                                                                                                                                                                                                                                            .error-message {
                                                                                                                                                                                                                                                                                                                background: #f8d7da !important;
                                                                                                                                                                                                                                                                                                                color: #721c24 !important;
                                                                                                                                                                                                                                                                                                                border: 1px solid #f5c6cb;
                                                                                                                                                                                                                                                                                                            }
                                                                                                                                                                                                                                                                                                        `;
        document.head.appendChild(style);
    </script>
<?php $__env->stopPush(); ?><?php /**PATH C:\Users\<USER>\OneDrive\Desktop\N\test\locaspace\resources\views/components/chat-bot.blade.php ENDPATH**/ ?>
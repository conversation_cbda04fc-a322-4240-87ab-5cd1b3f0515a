@extends('layouts.app')

@section('content')
<div class="container">
    <!-- Breadcrumb -->
    <nav aria-label="breadcrumb" class="mb-4">
        <ol class="breadcrumb">
            <li class="breadcrumb-item"><a href="{{ route('home') }}">Accueil</a></li>
            <li class="breadcrumb-item"><a href="{{ route('locals.index') }}">Locaux</a></li>
            <li class="breadcrumb-item"><a href="{{ route('locals.show', $local) }}">{{ $local->name }}</a></li>
            <li class="breadcrumb-item active">Réservation</li>
        </ol>
    </nav>

    <div class="row">
        <!-- Reservation Form -->
        <div class="col-lg-8">
            <div class="card">
                <div class="card-header">
                    <h4 class="mb-0">
                        <i class="fas fa-calendar-plus me-2"></i>Nouvelle réservation
                    </h4>
                </div>
                <div class="card-body">
                    <!-- Display validation errors -->
                    @if($errors->any())
                        <div class="alert alert-danger">
                            <h6><i class="fas fa-exclamation-triangle me-2"></i>Erreurs :</h6>
                            <ul class="mb-0">
                                @foreach($errors->all() as $error)
                                    <li>{{ $error }}</li>
                                @endforeach
                            </ul>
                        </div>
                    @endif

                    <form method="POST" action="{{ route('reservations.store') }}" id="reservationForm">
                        @csrf
                        <input type="hidden" name="local_id" value="{{ $local->id }}">

                        <!-- Date Selection -->
                        <div class="row mb-4">
                            <div class="col-md-6">
                                <label for="date" class="form-label">
                                    <i class="fas fa-calendar me-1"></i>Date de réservation *
                                </label>
                                <input type="date"
                                       class="form-control @error('date') is-invalid @enderror"
                                       id="date"
                                       name="date"
                                       value="{{ old('date', now()->addDay()->format('Y-m-d')) }}"
                                       min="{{ now()->format('Y-m-d') }}"
                                       required>
                                @error('date')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                            </div>
                        </div>

                        <!-- Billing Type Selection -->
                        <div class="row mb-4">
                            <div class="col-12">
                                <label class="form-label">
                                    <i class="fas fa-tag me-1"></i>Type de facturation *
                                </label>
                                @if($local->pricing_type === 'fixed')
                                    <!-- Fixed pricing -->
                                    <div class="card border-primary">
                                        <div class="card-body">
                                            <div class="form-check">
                                                <input class="form-check-input" type="radio" name="selected_pricing_type"
                                                       id="fixed" value="fixed" checked required>
                                                <label class="form-check-label" for="fixed">
                                                    <strong>Prix fixe - {{ $local->price }} MAD</strong>
                                                    <br><small class="text-muted">Prix unique pour la réservation</small>
                                                </label>
                                            </div>
                                        </div>
                                    </div>
                                @else
                                    <!-- Flexible pricing options -->
                                    <div class="row">
                                        @if($local->hourly_price && $local->hourly_price > 0)
                                        <div class="col-md-6 mb-3">
                                            <div class="card border-info">
                                                <div class="card-body">
                                                    <div class="form-check">
                                                        <input class="form-check-input" type="radio" name="selected_pricing_type"
                                                               id="hourly" value="hourly" {{ old('selected_pricing_type') == 'hourly' ? 'checked' : '' }} required>
                                                        <label class="form-check-label" for="hourly">
                                                            <strong>Horaire - {{ $local->hourly_price }} MAD/heure</strong>
                                                            <br><small class="text-muted">Facturation à l'heure</small>
                                                        </label>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                        @endif

                                        @if($local->daily_price && $local->daily_price > 0)
                                        <div class="col-md-6 mb-3">
                                            <div class="card border-success">
                                                <div class="card-body">
                                                    <div class="form-check">
                                                        <input class="form-check-input" type="radio" name="selected_pricing_type"
                                                               id="daily" value="daily" {{ old('selected_pricing_type') == 'daily' ? 'checked' : '' }} required>
                                                        <label class="form-check-label" for="daily">
                                                            <strong>Journalier - {{ $local->daily_price }} MAD/jour</strong>
                                                            <br><small class="text-muted">Facturation par jour</small>
                                                        </label>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                        @endif

                                        @if($local->weekly_price && $local->weekly_price > 0)
                                        <div class="col-md-6 mb-3">
                                            <div class="card border-warning">
                                                <div class="card-body">
                                                    <div class="form-check">
                                                        <input class="form-check-input" type="radio" name="selected_pricing_type"
                                                               id="weekly" value="weekly" {{ old('selected_pricing_type') == 'weekly' ? 'checked' : '' }} required>
                                                        <label class="form-check-label" for="weekly">
                                                            <strong>Hebdomadaire - {{ $local->weekly_price }} MAD/semaine</strong>
                                                            <br><small class="text-muted">Facturation par semaine</small>
                                                        </label>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                        @endif

                                        @if($local->monthly_price && $local->monthly_price > 0)
                                        <div class="col-md-6 mb-3">
                                            <div class="card border-danger">
                                                <div class="card-body">
                                                    <div class="form-check">
                                                        <input class="form-check-input" type="radio" name="selected_pricing_type"
                                                               id="monthly" value="monthly" {{ old('selected_pricing_type') == 'monthly' ? 'checked' : '' }} required>
                                                        <label class="form-check-label" for="monthly">
                                                            <strong>Mensuel - {{ $local->monthly_price }} MAD/mois</strong>
                                                            <br><small class="text-muted">Facturation par mois</small>
                                                        </label>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                        @endif
                                    </div>
                                @endif
                                @error('selected_pricing_type')
                                    <div class="invalid-feedback d-block">{{ $message }}</div>
                                @enderror
                            </div>
                        </div>

                        <!-- Duration/Quantity Input (for non-hourly billing) -->
                        <div class="row mb-4" id="duration_section" style="display: none;">
                            <div class="col-md-6">
                                <label for="duration_count" class="form-label">
                                    <i class="fas fa-hashtag me-1"></i><span id="duration_label">Quantité</span> *
                                </label>
                                <input type="number"
                                       class="form-control @error('duration_count') is-invalid @enderror"
                                       id="duration_count"
                                       name="duration_count"
                                       value="{{ old('duration_count', 1) }}"
                                       min="1"
                                       step="1">
                                @error('duration_count')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                                <div class="form-text" id="duration_help">Spécifiez la quantité</div>
                            </div>
                        </div>

                        <!-- Time Selection (for hourly billing) -->
                        <div class="row mb-4" id="time_section" style="display: none;">
                            <div class="col-md-6">
                                <label for="start_time" class="form-label">
                                    <i class="fas fa-play me-1"></i>Heure de début *
                                </label>
                                <input type="time"
                                       class="form-control @error('start_time') is-invalid @enderror"
                                       id="start_time"
                                       name="start_time"
                                       value="{{ old('start_time', '09:00') }}">
                                @error('start_time')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                            </div>
                            <div class="col-md-6">
                                <label for="end_time" class="form-label">
                                    <i class="fas fa-stop me-1"></i>Heure de fin *
                                </label>
                                <input type="time"
                                       class="form-control @error('end_time') is-invalid @enderror"
                                       id="end_time"
                                       name="end_time"
                                       value="{{ old('end_time', '10:00') }}">
                                @error('end_time')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                            </div>
                        </div>

                        <!-- Price Display -->
                        <div class="row mb-4">
                            <div class="col-12">
                                <div class="card bg-light">
                                    <div class="card-body text-center">
                                        <h5 class="mb-0">
                                            <i class="fas fa-calculator me-2"></i>Prix total:
                                            <span id="total_price" class="text-success fw-bold">0 MAD</span>
                                        </h5>
                                        <small class="text-muted" id="price_details">Sélectionnez un type de facturation</small>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Hidden fields -->
                        <input type="hidden" id="duration_unit" name="duration_unit" value="">
                        <input type="hidden" id="all_day" name="all_day" value="0">

                        <!-- Terms and Conditions -->
                        <div class="row mb-4">
                            <div class="col-12">
                                <div class="form-check">
                                    <input type="checkbox" class="form-check-input" id="terms" required>
                                    <label class="form-check-label" for="terms">
                                        J'accepte les <a href="#" class="text-decoration-none">conditions d'utilisation</a>
                                        et la <a href="#" class="text-decoration-none">politique de remboursement</a>
                                    </label>
                                </div>
                            </div>
                        </div>

                        <!-- Submit Buttons -->
                        <div class="row">
                            <div class="col-12">
                                <div class="d-flex justify-content-between">
                                    <a href="{{ route('locals.show', $local) }}" class="btn btn-outline-secondary">
                                        <i class="fas fa-arrow-left me-2"></i>Retour
                                    </a>
                                    <button type="submit" class="btn btn-primary btn-lg">
                                        <i class="fas fa-credit-card me-2"></i>Procéder au paiement
                                    </button>
                                </div>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
        </div>

        <!-- Sidebar -->
        <div class="col-lg-4">
            <!-- Local Summary -->
            <div class="card sticky-top" style="top: 20px;">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="fas fa-building me-2"></i>Récapitulatif
                    </h5>
                </div>
                <div class="card-body">
                    <div class="text-center mb-3">
                        @if($local->image)
                            <div class="position-relative">
                                <img src="{{ asset('storage/' . $local->image) }}"
                                     alt="{{ $local->name }}"
                                     class="img-fluid rounded shadow-sm mb-2"
                                     style="max-height: 200px; width: 100%; object-fit: cover;">
                                <!-- Type badge overlay -->
                                <span class="position-absolute top-0 end-0 m-2">
                                    @if($local->type === 'sport')
                                        <span class="badge bg-success">
                                            <i class="fas fa-futbol me-1"></i>Sport
                                        </span>
                                    @elseif($local->type === 'conference')
                                        <span class="badge bg-primary">
                                            <i class="fas fa-presentation-screen me-1"></i>Conférence
                                        </span>
                                    @else
                                        <span class="badge bg-warning">
                                            <i class="fas fa-glass-cheers me-1"></i>Fête
                                        </span>
                                    @endif
                                </span>
                            </div>
                        @else
                            <!-- Fallback to icon if no image -->
                            @if($local->type === 'sport')
                                <i class="fas fa-futbol text-success fa-3x mb-2"></i>
                            @elseif($local->type === 'conference')
                                <i class="fas fa-presentation-screen text-primary fa-3x mb-2"></i>
                            @else
                                <i class="fas fa-glass-cheers text-warning fa-3x mb-2"></i>
                            @endif
                        @endif
                    </div>

                    <h5 class="text-center">{{ $local->name }}</h5>

                    <ul class="list-unstyled">
                        <li class="mb-2">
                            <i class="fas fa-map-marker-alt text-muted me-2"></i>
                            {{ $local->location }}
                        </li>
                        <li class="mb-2">
                            <i class="fas fa-users text-muted me-2"></i>
                            Capacité : {{ $local->capacity }} personnes
                        </li>
                        <li class="mb-2">
                            <i class="fas fa-tag text-muted me-2"></i>
                            Type : {{ ucfirst($local->type) }}
                        </li>
                        <li class="mb-2">
                            <i class="fas fa-euro-sign text-muted me-2"></i>
                            @if($local->pricing_type === 'flexible')
                                Tarification flexible :
                                <div class="ms-3 mt-1">
                                    @if($local->hourly_price)
                                        <small class="d-block">• {{ $local->hourly_price }} MAD/heure</small>
                                    @endif
                                    @if($local->daily_price)
                                        <small class="d-block">• {{ $local->daily_price }} MAD/jour</small>
                                    @endif
                                    @if($local->weekly_price)
                                        <small class="d-block">• {{ $local->weekly_price }} MAD/semaine</small>
                                    @endif
                                    @if($local->monthly_price)
                                        <small class="d-block">• {{ $local->monthly_price }} MAD/mois</small>
                                    @endif
                                </div>
                            @else
                                Prix fixe : {{ $local->price }} MAD/réservation
                            @endif
                        </li>
                    </ul>

                    @if($local->equipment && count($local->equipment) > 0)
                    <hr>
                    <h6>Équipements inclus :</h6>
                    <div class="d-flex flex-wrap gap-1">
                        @foreach($local->equipment as $equipment)
                            <span class="badge bg-light text-dark">
                                @if($equipment === 'wifi')
                                    <i class="fas fa-wifi me-1"></i>WiFi
                                @elseif($equipment === 'projecteur')
                                    <i class="fas fa-video me-1"></i>Projecteur
                                @elseif($equipment === 'climatisation')
                                    <i class="fas fa-snowflake me-1"></i>Clim
                                @else
                                    {{ $equipment }}
                                @endif
                            </span>
                        @endforeach
                    </div>
                    @endif
                </div>
            </div>

            <!-- Help Card -->
            <div class="card mt-4">
                <div class="card-header">
                    <h6 class="mb-0">
                        <i class="fas fa-question-circle me-2"></i>Besoin d'aide ?
                    </h6>
                </div>
                <div class="card-body">
                    <p class="small mb-2">
                        <strong>Comment ça marche :</strong>
                    </p>
                    <ol class="small">
                        <li>Choisissez votre créneau</li>
                        <li>Vérifiez la disponibilité</li>
                        <li>Procédez au paiement</li>
                        <li>Recevez votre confirmation</li>
                    </ol>
                    <button type="button" class="btn btn-sm btn-outline-primary w-100">
                        <i class="fas fa-envelope me-1"></i>Nous contacter
                    </button>
                </div>
            </div>
        </div>
    </div>
</div>

@push('scripts')
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Pricing data from server
    const prices = {
        fixed: {{ $local->price ?? 0 }},
        hourly: {{ $local->hourly_price ?? 0 }},
        daily: {{ $local->daily_price ?? 0 }},
        weekly: {{ $local->weekly_price ?? 0 }},
        monthly: {{ $local->monthly_price ?? 0 }}
    };

    // Get DOM elements
    const durationSection = document.getElementById('duration_section');
    const timeSection = document.getElementById('time_section');
    const durationCountInput = document.getElementById('duration_count');
    const durationLabel = document.getElementById('duration_label');
    const durationHelp = document.getElementById('duration_help');
    const startTimeInput = document.getElementById('start_time');
    const endTimeInput = document.getElementById('end_time');
    const totalPriceSpan = document.getElementById('total_price');
    const priceDetailsSpan = document.getElementById('price_details');
    const durationUnitInput = document.getElementById('duration_unit');
    const allDayInput = document.getElementById('all_day');

    // Handle pricing type selection
    function handlePricingTypeChange() {
        const selectedType = document.querySelector('input[name="selected_pricing_type"]:checked');
        if (!selectedType) return;

        const type = selectedType.value;
        console.log('Selected pricing type:', type);

        // Hide all sections first
        durationSection.style.display = 'none';
        timeSection.style.display = 'none';

        // Reset values
        durationCountInput.value = 1;
        startTimeInput.value = '09:00';
        endTimeInput.value = '10:00';
        allDayInput.value = '0';

        // Show appropriate section and update labels
        switch(type) {
            case 'hourly':
                timeSection.style.display = 'block';
                durationUnitInput.value = 'hours';
                priceDetailsSpan.textContent = 'Sélectionnez les heures de début et fin';
                break;
            case 'daily':
                durationSection.style.display = 'block';
                durationLabel.textContent = 'Nombre de jours';
                durationHelp.textContent = 'Combien de jours voulez-vous réserver ?';
                durationUnitInput.value = 'days';
                allDayInput.value = '1';
                break;
            case 'weekly':
                durationSection.style.display = 'block';
                durationLabel.textContent = 'Nombre de semaines';
                durationHelp.textContent = 'Combien de semaines voulez-vous réserver ?';
                durationUnitInput.value = 'weeks';
                allDayInput.value = '1';
                break;
            case 'monthly':
                durationSection.style.display = 'block';
                durationLabel.textContent = 'Nombre de mois';
                durationHelp.textContent = 'Combien de mois voulez-vous réserver ?';
                durationUnitInput.value = 'months';
                allDayInput.value = '1';
                break;
            case 'fixed':
                durationUnitInput.value = 'fixed';
                priceDetailsSpan.textContent = 'Prix fixe pour la réservation';
                break;
        }

        calculatePrice();
    }

    // Calculate total price
    function calculatePrice() {
        const selectedType = document.querySelector('input[name="selected_pricing_type"]:checked');
        if (!selectedType) {
            totalPriceSpan.textContent = '0 MAD';
            return;
        }

        const type = selectedType.value;
        let price = 0;
        let quantity = 1;

        switch(type) {
            case 'hourly':
                const startTime = startTimeInput.value;
                const endTime = endTimeInput.value;

                if (startTime && endTime) {
                    const start = new Date(`2000-01-01T${startTime}`);
                    const end = new Date(`2000-01-01T${endTime}`);
                    const diffMs = end - start;
                    const diffHours = diffMs / (1000 * 60 * 60);

                    if (diffHours > 0) {
                        quantity = diffHours;
                        price = prices.hourly * diffHours;
                        priceDetailsSpan.textContent = `${diffHours}h × ${prices.hourly} MAD/h`;
                    } else {
                        price = prices.hourly;
                        priceDetailsSpan.textContent = `${prices.hourly} MAD/h`;
                    }
                } else {
                    price = prices.hourly;
                    priceDetailsSpan.textContent = `${prices.hourly} MAD/h`;
                }
                break;
            case 'daily':
                quantity = parseInt(durationCountInput.value) || 1;
                price = prices.daily * quantity;
                priceDetailsSpan.textContent = `${quantity} jour(s) × ${prices.daily} MAD/jour`;
                break;
            case 'weekly':
                quantity = parseInt(durationCountInput.value) || 1;
                price = prices.weekly * quantity;
                priceDetailsSpan.textContent = `${quantity} semaine(s) × ${prices.weekly} MAD/semaine`;
                break;
            case 'monthly':
                quantity = parseInt(durationCountInput.value) || 1;
                price = prices.monthly * quantity;
                priceDetailsSpan.textContent = `${quantity} mois × ${prices.monthly} MAD/mois`;
                break;
            case 'fixed':
                price = prices.fixed;
                priceDetailsSpan.textContent = 'Prix fixe';
                break;
        }

        totalPriceSpan.textContent = Math.round(price) + ' MAD';
    }

    // Add event listeners
    document.querySelectorAll('input[name="selected_pricing_type"]').forEach(radio => {
        radio.addEventListener('change', handlePricingTypeChange);
    });

    durationCountInput.addEventListener('input', calculatePrice);
    startTimeInput.addEventListener('change', calculatePrice);
    endTimeInput.addEventListener('change', calculatePrice);

    // Initialize form
    const checkedRadio = document.querySelector('input[name="selected_pricing_type"]:checked');
    if (checkedRadio) {
        handlePricingTypeChange();
    }
});
</script>
@endpush

@push('styles')
<style>
.card {
    border: none;
    border-radius: 12px;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.08);
    transition: all 0.3s ease;
}

.card:hover {
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.12);
}

.card-header {
    border-radius: 12px 12px 0 0 !important;
    border-bottom: 1px solid rgba(0, 0, 0, 0.05);
    font-weight: 600;
}

.form-control, .form-select {
    border-radius: 8px;
    border: 2px solid #e9ecef;
    transition: all 0.3s ease;
    padding: 0.75rem 1rem;
}

.form-control:focus, .form-select:focus {
    border-color: #0d6efd;
    box-shadow: 0 0 0 0.2rem rgba(13, 110, 253, 0.15);
}

.btn {
    border-radius: 8px;
    font-weight: 500;
    transition: all 0.3s ease;
    padding: 0.75rem 1.5rem;
}

.btn-primary {
    background: linear-gradient(135deg, #0d6efd 0%, #6610f2 100%);
    border: none;
}

.btn-primary:hover {
    transform: translateY(-1px);
    box-shadow: 0 4px 15px rgba(13, 110, 253, 0.3);
}

.btn-success {
    background: linear-gradient(135deg, #198754 0%, #20c997 100%);
    border: none;
}

.btn-success:hover {
    transform: translateY(-1px);
    box-shadow: 0 4px 15px rgba(25, 135, 84, 0.3);
}

.btn-info {
    background: linear-gradient(135deg, #0dcaf0 0%, #6f42c1 100%);
    border: none;
}

.btn-info:hover {
    transform: translateY(-1px);
    box-shadow: 0 4px 15px rgba(13, 202, 240, 0.3);
}

.bg-light {
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%) !important;
}

.text-success {
    color: #198754 !important;
    font-weight: 600;
}

.sticky-top {
    position: sticky !important;
    top: 20px !important;
}

/* Alert styling */
.alert {
    border-radius: 8px;
    border: none;
}

.alert-success {
    background: linear-gradient(135deg, #d1e7dd 0%, #a3d9a4 100%);
    color: #0f5132;
}

.alert-danger {
    background: linear-gradient(135deg, #f8d7da 0%, #f1aeb5 100%);
    color: #842029;
}

.alert-warning {
    background: linear-gradient(135deg, #fff3cd 0%, #ffecb5 100%);
    color: #664d03;
}

.alert-info {
    background: linear-gradient(135deg, #d1ecf1 0%, #b8daff 100%);
    color: #055160;
}

/* Responsive design */
@media (max-width: 768px) {
    .sticky-top {
        position: relative !important;
        top: 0 !important;
    }

    .card-body {
        padding: 1rem;
    }

    .btn {
        padding: 0.5rem 1rem;
        font-size: 0.9rem;
    }
}
</style>
@endpush
@endsection
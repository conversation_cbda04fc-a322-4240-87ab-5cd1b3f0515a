<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use App\Models\Reservation;
use App\Models\Local;
use App\Models\Invoice;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Validator;
use Illuminate\Support\Facades\DB;

class ReservationController extends Controller
{
    /**
     * Display a listing of user's reservations.
     */
    public function index()
    {
        $reservations = Auth::user()->reservations()
            ->with(['local', 'invoice'])
            ->orderBy('date', 'desc')
            ->paginate(10);

        return view('reservations.index', compact('reservations'));
    }

    /**
     * Show the form for creating a new reservation.
     */
    public function create(Local $local)
    {
        // Check if user already has pending reservations for this local
        $existingPendingReservations = collect();
        if (Auth::check()) {
            $existingPendingReservations = Reservation::where('user_id', Auth::id())
                ->where('local_id', $local->id)
                ->where('status', 'en attente')
                ->orderBy('date')
                ->get();
        }

        return view('reservations.create', compact('local', 'existingPendingReservations'));
    }

    /**
     * Store a newly created reservation in storage.
     */
    public function store(Request $request)
    {
        // Debug: Log the incoming request data
        \Log::info('Reservation store request data:', $request->all());

        $validator = Validator::make($request->all(), [
            'local_id' => 'required|exists:locals,id',
            'date' => 'required|date|after_or_equal:today',
            'start_time' => 'required_unless:all_day,true|date_format:H:i',
            'end_time' => 'required_unless:all_day,true|date_format:H:i|after:start_time',
            'all_day' => 'nullable|boolean',
            'selected_pricing_type' => 'required|string|in:hourly,daily,weekly,monthly,yearly,fixed',
            'duration_count' => 'required|numeric|min:0.5',
            'duration_unit' => 'required|string|in:hours,days,weeks,months,years,fixed',
        ], [
            'selected_pricing_type.required' => 'Veuillez sélectionner un type de facturation.',
            'duration_count.required' => 'Veuillez spécifier la quantité.',
            'duration_count.min' => 'La quantité doit être d\'au moins 0.5.',
            'duration_unit.required' => 'L\'unité de durée est requise.',
        ]);

        if ($validator->fails()) {
            \Log::error('Reservation validation failed:', $validator->errors()->toArray());
            return back()->withErrors($validator)->withInput();
        }

        $local = Local::findOrFail($request->local_id);

        // Handle all-day reservations
        if ($request->boolean('all_day')) {
            $startTime = '00:00';
            $endTime = '23:59';
        } else {
            $startTime = $request->start_time;
            $endTime = $request->end_time;
        }

        // Check if user already has a pending reservation for the same local and date
        $existingPendingReservation = Reservation::where('user_id', Auth::id())
            ->where('local_id', $request->local_id)
            ->where('date', $request->date)
            ->where('status', 'en attente')
            ->first();

        if ($existingPendingReservation) {
            return back()->withErrors([
                'duplicate' => "Vous avez déjà une réservation en attente pour ce local à cette date ({$existingPendingReservation->start_time->format('H:i')} - {$existingPendingReservation->end_time->format('H:i')}). Veuillez finaliser ou annuler votre réservation existante avant d'en créer une nouvelle.",
            ])->withInput();
        }

        // Check availability
        if (!$local->isAvailable($request->date, $startTime, $endTime)) {
            $errorMessage = $request->boolean('all_day')
                ? 'Ce local n\'est pas disponible toute la journée pour cette date.'
                : 'Ce créneau n\'est pas disponible.';
            return back()->withErrors([
                'time' => $errorMessage,
            ])->withInput();
        }

        DB::beginTransaction();

        try {
            // Create reservation
            $reservation = Reservation::create([
                'user_id' => Auth::id(),
                'local_id' => $request->local_id,
                'date' => $request->date,
                'start_time' => $startTime,
                'end_time' => $endTime,
                'status' => 'en attente',
                'selected_pricing_type' => $request->selected_pricing_type ?? ($request->boolean('all_day') ? 'daily' : 'hourly'),
                'duration_count' => $request->duration_count ?? ($request->boolean('all_day') ? 1 : 1),
                'duration_unit' => $request->duration_unit ?? ($request->boolean('all_day') ? 'days' : 'hours'),
                'all_day' => $request->boolean('all_day'),
            ]);

            // Calculate amount and create invoice
            $amount = $reservation->calculateAmount();
            $invoice = Invoice::create([
                'reservation_id' => $reservation->id,
                'amount' => $amount,
                'payment_status' => 'non réglé',
            ]);

            DB::commit();

            return redirect()->route('reservations.show', $reservation)
                ->with('success', 'Réservation créée avec succès. Veuillez procéder au paiement.');

        } catch (\Exception $e) {
            DB::rollback();
            return back()->withErrors([
                'error' => 'Une erreur est survenue lors de la création de la réservation.',
            ])->withInput();
        }
    }

    /**
     * Display the specified reservation.
     */
    public function show(Request $request, Reservation $reservation)
    {
        // Check if user owns this reservation or is admin
        if ($reservation->user_id !== Auth::id() && !Auth::user()->isAdmin()) {
            abort(403);
        }

        // Handle payment success callback from Stripe
        if ($request->get('payment') === 'success') {
            $this->handlePaymentSuccess($reservation);
        }

        $reservation->load(['local', 'invoice']);
        return view('reservations.show', compact('reservation'));
    }

    /**
     * Handle successful payment from Stripe
     */
    private function handlePaymentSuccess(Reservation $reservation)
    {
        try {
            // Update invoice status
            if ($reservation->invoice && !$reservation->invoice->isPaid()) {
                $reservation->invoice->update([
                    'payment_status' => 'réglé',
                    'paid_at' => now()
                ]);
            }

            // Update reservation status
            if ($reservation->status !== 'confirmée') {
                $reservation->update([
                    'status' => 'confirmée'
                ]);
            }

            session()->flash('success', 'Paiement effectué avec succès ! Votre réservation est maintenant confirmée.');

        } catch (\Exception $e) {
            \Log::error('Error handling payment success', [
                'reservation_id' => $reservation->id,
                'error' => $e->getMessage()
            ]);
            session()->flash('warning', 'Paiement reçu, mais une erreur est survenue lors de la mise à jour. Contactez le support si nécessaire.');
        }
    }

    /**
     * Cancel a reservation.
     */
    public function cancel(Reservation $reservation)
    {
        // Check if user owns this reservation
        if ($reservation->user_id !== Auth::id()) {
            abort(403);
        }

        // Can only cancel pending or confirmed reservations
        if (!in_array($reservation->status, ['en attente', 'confirmée'])) {
            return back()->withErrors([
                'error' => 'Cette réservation ne peut pas être annulée.',
            ]);
        }

        $reservation->update(['status' => 'annulée']);

        return redirect()->route('reservations.index')
            ->with('success', 'Réservation annulée avec succès.');
    }

    /**
     * Confirm a reservation (admin only).
     */
    public function confirm(Reservation $reservation)
    {
        if (!Auth::user()->isAdmin()) {
            abort(403);
        }

        $reservation->update(['status' => 'confirmée']);

        return back()->with('success', 'Réservation confirmée avec succès.');
    }

    /**
     * Get calendar data for a specific local.
     */
    public function calendar(Request $request, Local $local)
    {
        $month = $request->get('month', now()->month);
        $year = $request->get('year', now()->year);

        $reservations = $local->reservations()
            ->whereYear('date', $year)
            ->whereMonth('date', $month)
            ->where('status', '!=', 'annulée')
            ->get(['date', 'start_time', 'end_time', 'status']);

        $events = $reservations->map(function ($reservation) {
            return [
                'title' => $reservation->status === 'confirmée' ? 'Réservé' : 'En attente',
                'start' => $reservation->date->format('Y-m-d') . 'T' . $reservation->start_time,
                'end' => $reservation->date->format('Y-m-d') . 'T' . $reservation->end_time,
                'color' => $reservation->status === 'confirmée' ? '#dc3545' : '#ffc107',
            ];
        });

        return response()->json($events);
    }
}

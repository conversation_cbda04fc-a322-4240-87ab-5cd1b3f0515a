<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use App\Models\Reservation;
use App\Models\Local;
use App\Models\Invoice;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Validator;
use Illuminate\Support\Facades\DB;

class ReservationController extends Controller
{
    /**
     * Display a listing of user's reservations.
     */
    public function index()
    {
        $reservations = Auth::user()->reservations()
            ->with(['local', 'invoice'])
            ->orderBy('date', 'desc')
            ->paginate(10);

        return view('reservations.index', compact('reservations'));
    }

    /**
     * Show the form for creating a new reservation.
     */
    public function create(Local $local)
    {
        // Check if user already has pending reservations for this local
        $existingPendingReservations = collect();
        if (Auth::check()) {
            $existingPendingReservations = Reservation::where('user_id', Auth::id())
                ->where('local_id', $local->id)
                ->where('status', 'en attente')
                ->orderBy('date')
                ->get();
        }

        return view('reservations.create', compact('local', 'existingPendingReservations'));
    }

    /**
     * Store a newly created reservation in storage.
     */
    public function store(Request $request)
    {
        // Debug: Log the incoming request data
        \Log::info('Reservation store request data:', $request->all());

        $validator = Validator::make($request->all(), [
            'local_id' => 'required|exists:locals,id',
            'date' => 'required|date|after_or_equal:today',
            'selected_pricing_type' => 'required|string|in:hourly,daily,weekly,monthly,fixed',
            'start_time' => 'required_if:selected_pricing_type,hourly|nullable|date_format:H:i',
            'end_time' => 'required_if:selected_pricing_type,hourly|nullable|date_format:H:i|after:start_time',
            'duration_count' => 'required_unless:selected_pricing_type,hourly,fixed|nullable|integer|min:1',
            'duration_unit' => 'required|string|in:hours,days,weeks,months,fixed',
            'all_day' => 'nullable|boolean',
        ], [
            'selected_pricing_type.required' => 'Veuillez sélectionner un type de facturation.',
            'start_time.required_if' => 'L\'heure de début est requise pour la facturation horaire.',
            'end_time.required_if' => 'L\'heure de fin est requise pour la facturation horaire.',
            'end_time.after' => 'L\'heure de fin doit être après l\'heure de début.',
            'duration_count.required_unless' => 'Veuillez spécifier la quantité.',
            'duration_count.min' => 'La quantité doit être d\'au moins 1.',
        ]);

        if ($validator->fails()) {
            \Log::error('Reservation validation failed:', $validator->errors()->toArray());
            return back()->withErrors($validator)->withInput();
        }

        $local = Local::findOrFail($request->local_id);

        // Determine start and end times based on pricing type
        if ($request->selected_pricing_type === 'hourly') {
            $startTime = $request->start_time;
            $endTime = $request->end_time;
            $durationCount = $this->calculateHours($startTime, $endTime);
            $allDay = false;
        } else {
            // For daily, weekly, monthly, or fixed pricing
            $startTime = '00:00';
            $endTime = '23:59';
            $durationCount = $request->duration_count ?? 1;
            $allDay = true;
        }

        // Check if user already has a pending reservation for the same local and date
        $existingPendingReservation = Reservation::where('user_id', Auth::id())
            ->where('local_id', $request->local_id)
            ->where('date', $request->date)
            ->where('status', 'en attente')
            ->first();

        if ($existingPendingReservation) {
            return back()->withErrors([
                'duplicate' => "Vous avez déjà une réservation en attente pour ce local à cette date. Veuillez finaliser ou annuler votre réservation existante avant d'en créer une nouvelle.",
            ])->withInput();
        }

        // Check availability
        if (!$local->isAvailable($request->date, $startTime, $endTime)) {
            $errorMessage = $allDay
                ? 'Ce local n\'est pas disponible pour cette date.'
                : 'Ce créneau n\'est pas disponible.';
            return back()->withErrors([
                'availability' => $errorMessage,
            ])->withInput();
        }

        DB::beginTransaction();

        try {
            // Create reservation
            $reservation = Reservation::create([
                'user_id' => Auth::id(),
                'local_id' => $request->local_id,
                'date' => $request->date,
                'start_time' => $startTime,
                'end_time' => $endTime,
                'status' => 'en attente',
                'selected_pricing_type' => $request->selected_pricing_type,
                'duration_count' => $durationCount,
                'duration_unit' => $request->duration_unit,
                'all_day' => $allDay,
            ]);

            // Calculate amount and create invoice
            $amount = $reservation->calculateAmount();
            $invoice = Invoice::create([
                'reservation_id' => $reservation->id,
                'amount' => $amount,
                'payment_status' => 'non réglé',
            ]);

            DB::commit();

            return redirect()->route('reservations.show', $reservation)
                ->with('success', 'Réservation créée avec succès. Veuillez procéder au paiement.');

        } catch (\Exception $e) {
            DB::rollback();
            \Log::error('Reservation creation failed:', ['error' => $e->getMessage()]);
            return back()->withErrors([
                'error' => 'Une erreur est survenue lors de la création de la réservation.',
            ])->withInput();
        }
    }

    /**
     * Calculate hours between start and end time
     */
    private function calculateHours($startTime, $endTime)
    {
        $start = \Carbon\Carbon::createFromFormat('H:i', $startTime);
        $end = \Carbon\Carbon::createFromFormat('H:i', $endTime);

        if ($end->lt($start)) {
            $end->addDay();
        }

        return $start->diffInHours($end, false);
    }

    /**
     * Display the specified reservation.
     */
    public function show(Request $request, Reservation $reservation)
    {
        // Check if user owns this reservation or is admin
        if ($reservation->user_id !== Auth::id() && !Auth::user()->isAdmin()) {
            abort(403);
        }

        // Handle payment success callback from Stripe
        if ($request->get('payment') === 'success') {
            $this->handlePaymentSuccess($reservation);
        }

        $reservation->load(['local', 'invoice']);
        return view('reservations.show', compact('reservation'));
    }

    /**
     * Handle successful payment from Stripe
     */
    private function handlePaymentSuccess(Reservation $reservation)
    {
        try {
            // Update invoice status
            if ($reservation->invoice && !$reservation->invoice->isPaid()) {
                $reservation->invoice->update([
                    'payment_status' => 'réglé',
                    'paid_at' => now()
                ]);
            }

            // Update reservation status
            if ($reservation->status !== 'confirmée') {
                $reservation->update([
                    'status' => 'confirmée'
                ]);
            }

            session()->flash('success', 'Paiement effectué avec succès ! Votre réservation est maintenant confirmée.');

        } catch (\Exception $e) {
            \Log::error('Error handling payment success', [
                'reservation_id' => $reservation->id,
                'error' => $e->getMessage()
            ]);
            session()->flash('warning', 'Paiement reçu, mais une erreur est survenue lors de la mise à jour. Contactez le support si nécessaire.');
        }
    }

    /**
     * Cancel a reservation.
     */
    public function cancel(Reservation $reservation)
    {
        // Check if user owns this reservation
        if ($reservation->user_id !== Auth::id()) {
            abort(403);
        }

        // Can only cancel pending or confirmed reservations
        if (!in_array($reservation->status, ['en attente', 'confirmée'])) {
            return back()->withErrors([
                'error' => 'Cette réservation ne peut pas être annulée.',
            ]);
        }

        $reservation->update(['status' => 'annulée']);

        return redirect()->route('reservations.index')
            ->with('success', 'Réservation annulée avec succès.');
    }

    /**
     * Confirm a reservation (admin only).
     */
    public function confirm(Reservation $reservation)
    {
        if (!Auth::user()->isAdmin()) {
            abort(403);
        }

        $reservation->update(['status' => 'confirmée']);

        return back()->with('success', 'Réservation confirmée avec succès.');
    }

    /**
     * Get calendar data for a specific local.
     */
    public function calendar(Request $request, Local $local)
    {
        $month = $request->get('month', now()->month);
        $year = $request->get('year', now()->year);

        $reservations = $local->reservations()
            ->whereYear('date', $year)
            ->whereMonth('date', $month)
            ->where('status', '!=', 'annulée')
            ->get(['date', 'start_time', 'end_time', 'status']);

        $events = $reservations->map(function ($reservation) {
            return [
                'title' => $reservation->status === 'confirmée' ? 'Réservé' : 'En attente',
                'start' => $reservation->date->format('Y-m-d') . 'T' . $reservation->start_time,
                'end' => $reservation->date->format('Y-m-d') . 'T' . $reservation->end_time,
                'color' => $reservation->status === 'confirmée' ? '#dc3545' : '#ffc107',
            ];
        });

        return response()->json($events);
    }
}

@extends('layouts.app')

@section('content')

    <!-- Section Hero -->
    <section class="position-relative overflow-hidden bg-white" style="min-height: 100vh;">
        <!-- Background Animation -->
        <div class="position-absolute w-100 h-100" style="opacity: 0.05;">
            <div class="position-absolute" style="top: 10%; left: 10%; animation: float 6s ease-in-out infinite;">
                <i class="fas fa-building fa-3x text-primary"></i>
            </div>
            <div class="position-absolute" style="top: 20%; right: 15%; animation: float 8s ease-in-out infinite reverse;">
                <i class="fas fa-futbol fa-2x text-success"></i>
            </div>
            <div class="position-absolute" style="bottom: 30%; left: 20%; animation: float 7s ease-in-out infinite;">
                <i class="fas fa-glass-cheers fa-2x text-warning"></i>
            </div>
        </div>

        <div class="container h-100 d-flex align-items-center">
            <div class="row w-100 align-items-center">
                <div class="col-lg-6 fade-in-left">
                    <div class="hero-content text-dark">
                        <h1 class="display-3 fw-bold mb-4 text-dark">
                            Réservez vos espaces en <span class="text-primary">quelques clics</span>
                        </h1>
                        <p class="lead mb-4 text-muted">
                            Découvrez {{ $stats['total_locals'] }} locaux disponibles avec {{ $stats['total_reservations'] }} réservations confirmées.
                            Rejoignez {{ $stats['total_users'] }} utilisateurs satisfaits !
                        </p>
                        <div class="d-flex flex-column flex-md-row gap-3 mb-4">
                            <a href="{{ route('locals.index') }}" class="btn btn-primary btn-lg px-4 py-3">
                                <i class="fas fa-search me-2"></i>Découvrir nos locaux
                            </a>
                            @guest
                                <a href="{{ route('register') }}" class="btn btn-outline-primary btn-lg px-4 py-3">
                                    <i class="fas fa-user-plus me-2"></i>S'inscrire gratuitement
                                </a>
                            @endguest
                        </div>

                        <!-- Quick Stats -->
                        <div class="row text-center mt-5">
                            <div class="col-4">
                                <h3 class="text-primary fw-bold">50+</h3>
                                <small class="text-muted">Locaux</small>
                            </div>
                            <div class="col-4">
                                <h3 class="text-primary fw-bold">1000+</h3>
                                <small class="text-muted">Réservations</small>
                            </div>
                            <div class="col-4">
                                <h3 class="text-primary fw-bold">24/7</h3>
                                <small class="text-muted">Support</small>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-lg-6 fade-in-right">
                    <div class="text-center position-relative">

                        <div class="hero-logo-container text-center" style="background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
        padding: 40px;
        border-radius: 20px;
        box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
        border: 1px solid #dee2e6;">

                            <div style="background: #ffffff; padding: 15px; border-radius: 15px; display: inline-block; box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);">
                                <img src="{{ asset('images/logo.png') }}" alt="LocaSpace Logo"
                                    style="height: 100px; width: auto;">
                            </div>

                            <h2 class="text-dark fw-bold mb-1 mt-3">LocaSpace</h2>
                            <p class="text-muted mb-0">Votre plateforme de réservation</p>
                        </div>


                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Section Types de locaux -->
    <section id="locaux" class="py-5">
        <div class="container">
            <div class="text-center mb-5">
                <h2 class="display-5 fw-bold mb-3">Types de locaux disponibles</h2>
                <p class="lead text-muted">Choisissez le type d'espace qui correspond à vos besoins</p>
            </div>
            <div class="row g-4">
                @foreach($localTypes as $typeKey => $type)
                <div class="col-md-4">
                    <div class="card h-100 shadow-sm border-0 hover-lift">
                        <div class="card-body text-center p-4">
                            <div class="mb-3">
                                <i class="{{ $type['icon'] }} text-{{ $type['color'] }}" style="font-size: 3rem;"></i>
                            </div>
                            <h3 class="card-title h4 fw-bold">{{ $type['name'] }}</h3>
                            <p class="card-text text-muted">{{ $type['description'] }}</p>

                            <!-- Statistics -->
                            <div class="mb-3">
                                <span class="badge bg-{{ $type['color'] }} bg-opacity-10 text-{{ $type['color'] }} px-3 py-2">
                                    <i class="fas fa-building me-1"></i>{{ $type['count'] }} locaux disponibles
                                </span>
                            </div>

                            <!-- Latest locals preview -->
                            @if($type['latest']->count() > 0)
                            <div class="mb-3">
                                <small class="text-muted d-block mb-2">Derniers ajouts :</small>
                                <div class="d-flex flex-wrap gap-1 justify-content-center">
                                    @foreach($type['latest']->take(2) as $local)
                                    <span class="badge bg-light text-dark small">{{ $local->name }}</span>
                                    @endforeach
                                    @if($type['latest']->count() > 2)
                                    <span class="badge bg-secondary small">+{{ $type['latest']->count() - 2 }}</span>
                                    @endif
                                </div>
                            </div>
                            @endif

                            <a href="{{ route('locals.index', ['type' => $typeKey]) }}" class="btn btn-outline-{{ $type['color'] }}">
                                Voir les locaux <i class="fas fa-arrow-right ms-1"></i>
                            </a>
                        </div>
                    </div>
                </div>
                @endforeach
            </div>
        </div>
    </section>

    <!-- Section Statistiques -->
    <section class="py-5 bg-light">
        <div class="container">
            <div class="text-center mb-5">
                <h2 class="display-5 fw-bold mb-3">LocaSpace en chiffres</h2>
                <p class="lead text-muted">La confiance de nos utilisateurs en quelques statistiques</p>
            </div>
            <div class="row g-4">
                <div class="col-md-3 col-sm-6">
                    <div class="card border-0 shadow-sm text-center h-100">
                        <div class="card-body p-4">
                            <div class="mb-3">
                                <i class="fas fa-building text-primary" style="font-size: 2.5rem;"></i>
                            </div>
                            <h3 class="h2 fw-bold text-primary mb-1">{{ $stats['total_locals'] }}</h3>
                            <p class="text-muted mb-0">Locaux disponibles</p>
                        </div>
                    </div>
                </div>
                <div class="col-md-3 col-sm-6">
                    <div class="card border-0 shadow-sm text-center h-100">
                        <div class="card-body p-4">
                            <div class="mb-3">
                                <i class="fas fa-calendar-check text-success" style="font-size: 2.5rem;"></i>
                            </div>
                            <h3 class="h2 fw-bold text-success mb-1">{{ $stats['total_reservations'] }}</h3>
                            <p class="text-muted mb-0">Réservations confirmées</p>
                        </div>
                    </div>
                </div>
                <div class="col-md-3 col-sm-6">
                    <div class="card border-0 shadow-sm text-center h-100">
                        <div class="card-body p-4">
                            <div class="mb-3">
                                <i class="fas fa-users text-info" style="font-size: 2.5rem;"></i>
                            </div>
                            <h3 class="h2 fw-bold text-info mb-1">{{ $stats['total_users'] }}</h3>
                            <p class="text-muted mb-0">Utilisateurs actifs</p>
                        </div>
                    </div>
                </div>
                <div class="col-md-3 col-sm-6">
                    <div class="card border-0 shadow-sm text-center h-100">
                        <div class="card-body p-4">
                            <div class="mb-3">
                                <i class="fas fa-store text-warning" style="font-size: 2.5rem;"></i>
                            </div>
                            <h3 class="h2 fw-bold text-warning mb-1">{{ $stats['total_sellers'] }}</h3>
                            <p class="text-muted mb-0">Propriétaires partenaires</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Section Locaux populaires -->
    @if($popularLocals->count() > 0)
    <section class="py-5">
        <div class="container">
            <div class="text-center mb-5">
                <h2 class="display-5 fw-bold mb-3">Locaux les plus populaires</h2>
                <p class="lead text-muted">Découvrez les espaces préférés de nos utilisateurs</p>
            </div>
            <div class="row g-4">
                @foreach($popularLocals as $local)
                <div class="col-lg-3 col-md-6">
                    <div class="card h-100 border-0 shadow-sm hover-lift">
                        <div class="position-relative">
                            @if($local->image)
                                <img src="{{ asset('storage/' . $local->image) }}"
                                     alt="{{ $local->name }}"
                                     class="card-img-top"
                                     style="height: 200px; object-fit: cover;">
                            @else
                                <div class="card-img-top bg-gradient-primary d-flex align-items-center justify-content-center"
                                     style="height: 200px;">
                                    @if($local->type === 'sport')
                                        <i class="fas fa-futbol text-white fa-3x"></i>
                                    @elseif($local->type === 'conference')
                                        <i class="fas fa-presentation-screen text-white fa-3x"></i>
                                    @else
                                        <i class="fas fa-glass-cheers text-white fa-3x"></i>
                                    @endif
                                </div>
                            @endif
                            <div class="position-absolute top-0 end-0 m-2">
                                <span class="badge bg-danger">
                                    <i class="fas fa-fire me-1"></i>Populaire
                                </span>
                            </div>
                        </div>
                        <div class="card-body">
                            <h5 class="card-title fw-bold">{{ $local->name }}</h5>
                            <p class="text-muted small mb-2">
                                <i class="fas fa-map-marker-alt me-1"></i>{{ $local->location }}
                            </p>
                            <p class="text-muted small mb-3">
                                <i class="fas fa-users me-1"></i>{{ $local->capacity }} personnes
                            </p>
                            <div class="d-flex justify-content-between align-items-center">
                                <div>
                                    @if($local->pricing_type === 'flexible')
                                        <span class="fw-bold text-primary">{{ $local->hourly_price }} MAD/h</span>
                                    @else
                                        <span class="fw-bold text-primary">{{ $local->price }} MAD</span>
                                    @endif
                                </div>
                                <small class="text-muted">
                                    <i class="fas fa-calendar-check me-1"></i>{{ $local->reservations_count }} réservations
                                </small>
                            </div>
                            <a href="{{ route('locals.show', $local) }}" class="btn btn-primary w-100 mt-3">
                                <i class="fas fa-eye me-2"></i>Voir détails
                            </a>
                        </div>
                    </div>
                </div>
                @endforeach
            </div>
        </div>
    </section>
    @endif

    <!-- Section Locaux récents -->
    @if($featuredLocals->count() > 0)
    <section class="py-5 bg-light">
        <div class="container">
            <div class="text-center mb-5">
                <h2 class="display-5 fw-bold mb-3">Derniers locaux ajoutés</h2>
                <p class="lead text-muted">Découvrez les nouveaux espaces disponibles</p>
            </div>
            <div class="row g-4">
                @foreach($featuredLocals->take(3) as $local)
                <div class="col-lg-4 col-md-6">
                    <div class="card h-100 border-0 shadow-sm hover-lift">
                        <div class="position-relative">
                            @if($local->image)
                                <img src="{{ asset('storage/' . $local->image) }}"
                                     alt="{{ $local->name }}"
                                     class="card-img-top"
                                     style="height: 200px; object-fit: cover;">
                            @else
                                <div class="card-img-top bg-gradient-secondary d-flex align-items-center justify-content-center"
                                     style="height: 200px;">
                                    @if($local->type === 'sport')
                                        <i class="fas fa-futbol text-white fa-3x"></i>
                                    @elseif($local->type === 'conference')
                                        <i class="fas fa-presentation-screen text-white fa-3x"></i>
                                    @else
                                        <i class="fas fa-glass-cheers text-white fa-3x"></i>
                                    @endif
                                </div>
                            @endif
                            <div class="position-absolute top-0 end-0 m-2">
                                <span class="badge bg-success">
                                    <i class="fas fa-star me-1"></i>Nouveau
                                </span>
                            </div>
                        </div>
                        <div class="card-body">
                            <h5 class="card-title fw-bold">{{ $local->name }}</h5>
                            <p class="text-muted small mb-2">
                                <i class="fas fa-map-marker-alt me-1"></i>{{ $local->location }}
                            </p>
                            <p class="text-muted small mb-3">
                                <i class="fas fa-users me-1"></i>{{ $local->capacity }} personnes
                            </p>
                            <div class="d-flex justify-content-between align-items-center">
                                <div>
                                    @if($local->pricing_type === 'flexible')
                                        <span class="fw-bold text-primary">{{ $local->hourly_price }} MAD/h</span>
                                    @else
                                        <span class="fw-bold text-primary">{{ $local->price }} MAD</span>
                                    @endif
                                </div>
                                <small class="text-success">
                                    <i class="fas fa-clock me-1"></i>Ajouté {{ $local->created_at->diffForHumans() }}
                                </small>
                            </div>
                            <a href="{{ route('locals.show', $local) }}" class="btn btn-outline-primary w-100 mt-3">
                                <i class="fas fa-eye me-2"></i>Découvrir
                            </a>
                        </div>
                    </div>
                </div>
                @endforeach
            </div>

            @if($featuredLocals->count() > 3)
            <div class="text-center mt-4">
                <a href="{{ route('locals.index') }}" class="btn btn-primary btn-lg">
                    <i class="fas fa-search me-2"></i>Voir tous les locaux
                </a>
            </div>
            @endif
        </div>
    </section>
    @endif

    <!-- Section Calendrier interactif -->
    <section class="py-5">
        <div class="container">
            <div class="text-center mb-5">
                <h2 class="display-5 fw-bold mb-3">Calendrier des disponibilités</h2>
                <p class="lead text-muted">Explorez les créneaux disponibles et les réservations en cours</p>
            </div>

            <div class="row">
                <div class="col-lg-8">
                    <!-- Calendar Container -->
                    <div class="card border-0 shadow-sm">
                        <div class="card-header bg-gradient-primary text-white">
                            <div class="d-flex justify-content-between align-items-center">
                                <h5 class="mb-0">
                                    <i class="fas fa-calendar-alt me-2"></i>
                                    <span id="currentMonth">{{ now()->format('F Y') }}</span>
                                </h5>
                                <div class="btn-group" role="group">
                                    <button type="button" class="btn btn-outline-light btn-sm" id="prevMonth">
                                        <i class="fas fa-chevron-left"></i>
                                    </button>
                                    <button type="button" class="btn btn-outline-light btn-sm" id="todayBtn">
                                        Aujourd'hui
                                    </button>
                                    <button type="button" class="btn btn-outline-light btn-sm" id="nextMonth">
                                        <i class="fas fa-chevron-right"></i>
                                    </button>
                                </div>
                            </div>
                        </div>
                        <div class="card-body p-0">
                            <div id="calendar" class="calendar-container"></div>
                        </div>
                    </div>
                </div>

                <div class="col-lg-4">
                    <!-- Calendar Legend -->
                    <div class="card border-0 shadow-sm mb-4">
                        <div class="card-header">
                            <h6 class="mb-0">
                                <i class="fas fa-info-circle me-2"></i>Légende
                            </h6>
                        </div>
                        <div class="card-body">
                            <div class="d-flex align-items-center mb-2">
                                <div class="calendar-legend-color bg-success me-2"></div>
                                <small>Disponible</small>
                            </div>
                            <div class="d-flex align-items-center mb-2">
                                <div class="calendar-legend-color bg-warning me-2"></div>
                                <small>Partiellement réservé</small>
                            </div>
                            <div class="d-flex align-items-center mb-2">
                                <div class="calendar-legend-color bg-danger me-2"></div>
                                <small>Complet</small>
                            </div>
                            <div class="d-flex align-items-center">
                                <div class="calendar-legend-color bg-secondary me-2"></div>
                                <small>Passé</small>
                            </div>
                        </div>
                    </div>

                    <!-- Selected Date Info -->
                    <div class="card border-0 shadow-sm">
                        <div class="card-header">
                            <h6 class="mb-0">
                                <i class="fas fa-calendar-day me-2"></i>
                                <span id="selectedDateTitle">Sélectionnez une date</span>
                            </h6>
                        </div>
                        <div class="card-body" id="selectedDateInfo">
                            <p class="text-muted text-center">
                                <i class="fas fa-hand-pointer fa-2x mb-2 d-block"></i>
                                Cliquez sur une date du calendrier pour voir les détails des réservations
                            </p>
                        </div>
                    </div>

                    <!-- Quick Actions -->
                    <div class="card border-0 shadow-sm mt-4">
                        <div class="card-header">
                            <h6 class="mb-0">
                                <i class="fas fa-bolt me-2"></i>Actions rapides
                            </h6>
                        </div>
                        <div class="card-body">
                            <a href="{{ route('locals.index') }}" class="btn btn-primary w-100 mb-2">
                                <i class="fas fa-search me-2"></i>Rechercher un local
                            </a>
                            @auth
                            <a href="{{ route('reservations.index') }}" class="btn btn-outline-primary w-100">
                                <i class="fas fa-calendar-check me-2"></i>Mes réservations
                            </a>
                            @else
                            <a href="{{ route('register') }}" class="btn btn-outline-primary w-100">
                                <i class="fas fa-user-plus me-2"></i>Créer un compte
                            </a>
                            @endauth
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>

@endsection

@push('styles')
<style>
/* Hover effects */
.hover-lift {
    transition: all 0.3s ease;
}

.hover-lift:hover {
    transform: translateY(-5px);
    box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15) !important;
}

/* Floating animation */
@keyframes float {
    0% {
        transform: translateY(0px);
    }
    50% {
        transform: translateY(-20px);
    }
    100% {
        transform: translateY(0px);
    }
}

/* Fade in animations */
.fade-in-left {
    animation: fadeInLeft 1s ease-out;
}

@keyframes fadeInLeft {
    from {
        opacity: 0;
        transform: translateX(-50px);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

.fade-in-up {
    animation: fadeInUp 0.8s ease-out;
}

@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* Card improvements */
.card {
    border-radius: 15px;
    overflow: hidden;
}

.card-img-top {
    border-radius: 15px 15px 0 0;
}

/* Badge improvements */
.badge {
    font-size: 0.75rem;
    padding: 0.5rem 0.75rem;
    border-radius: 20px;
}

/* Button improvements */
.btn {
    border-radius: 25px;
    font-weight: 500;
    transition: all 0.3s ease;
}

.btn:hover {
    transform: translateY(-2px);
}

/* Statistics section */
.stats-card {
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
    border-radius: 20px;
    padding: 2rem;
    text-align: center;
    transition: all 0.3s ease;
}

.stats-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 1rem 2rem rgba(0, 0, 0, 0.1);
}

/* Responsive improvements */
@media (max-width: 768px) {
    .display-3 {
        font-size: 2.5rem;
    }

    .hero-content {
        text-align: center;
    }

    .hero-logo-container {
        margin-top: 2rem;
    }
}

/* Loading animation for images */
.card-img-top {
    background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
    background-size: 200% 100%;
    animation: loading 1.5s infinite;
}

@keyframes loading {
    0% {
        background-position: 200% 0;
    }
    100% {
        background-position: -200% 0;
    }
}

.card-img-top img {
    animation: none;
    background: none;
}

/* Interactive Calendar Styles */
.calendar-container {
    background: white;
    border-radius: 0 0 15px 15px;
}

.calendar-grid {
    display: grid;
    grid-template-columns: repeat(7, 1fr);
    gap: 1px;
    background: #e9ecef;
    border-radius: 0 0 15px 15px;
    overflow: hidden;
}

.calendar-header {
    display: grid;
    grid-template-columns: repeat(7, 1fr);
    background: #f8f9fa;
    border-bottom: 2px solid #e9ecef;
}

.calendar-day-header {
    padding: 1rem 0.5rem;
    text-align: center;
    font-weight: 600;
    font-size: 0.875rem;
    color: #495057;
    background: #f8f9fa;
}

.calendar-day {
    background: white;
    min-height: 80px;
    padding: 0.5rem;
    cursor: pointer;
    transition: all 0.2s ease;
    position: relative;
    border: 2px solid transparent;
}

.calendar-day:hover {
    background: #f8f9ff;
    transform: scale(1.02);
    border-color: #007bff;
    z-index: 2;
}

.calendar-day.selected {
    background: #e7f3ff;
    border-color: #007bff;
    box-shadow: 0 0 0 2px rgba(0, 123, 255, 0.25);
}

.calendar-day.other-month {
    background: #f8f9fa;
    color: #adb5bd;
}

.calendar-day.other-month:hover {
    background: #e9ecef;
}

.calendar-day.past {
    background: #f8f9fa;
    color: #6c757d;
    cursor: not-allowed;
}

.calendar-day.past:hover {
    transform: none;
    border-color: transparent;
}

.calendar-day.today {
    background: linear-gradient(135deg, #007bff 0%, #0056b3 100%);
    color: white;
    font-weight: bold;
}

.calendar-day.today:hover {
    background: linear-gradient(135deg, #0056b3 0%, #004085 100%);
}

.calendar-day-number {
    font-weight: 600;
    margin-bottom: 0.25rem;
    font-size: 0.875rem;
}

.calendar-day-status {
    position: absolute;
    bottom: 0.25rem;
    left: 0.25rem;
    right: 0.25rem;
    height: 4px;
    border-radius: 2px;
    background: #28a745;
}

.calendar-day-status.partial {
    background: #ffc107;
}

.calendar-day-status.full {
    background: #dc3545;
}

.calendar-day-status.past {
    background: #6c757d;
}

.calendar-reservations {
    font-size: 0.6rem;
    color: #6c757d;
    margin-top: 0.125rem;
}

.calendar-legend-color {
    width: 16px;
    height: 16px;
    border-radius: 3px;
    flex-shrink: 0;
}

/* Reservation details */
.reservation-item {
    background: #f8f9fa;
    border-radius: 8px;
    padding: 0.75rem;
    margin-bottom: 0.5rem;
    border-left: 4px solid #007bff;
    transition: all 0.2s ease;
}

.reservation-item:hover {
    background: #e9ecef;
    transform: translateX(2px);
}

.reservation-item.confirmed {
    border-left-color: #28a745;
}

.reservation-item.pending {
    border-left-color: #ffc107;
}

.reservation-item.cancelled {
    border-left-color: #dc3545;
    opacity: 0.7;
}

/* Loading states */
.calendar-loading {
    display: flex;
    align-items: center;
    justify-content: center;
    min-height: 400px;
    color: #6c757d;
}

.calendar-loading i {
    animation: spin 1s linear infinite;
}

@keyframes spin {
    from { transform: rotate(0deg); }
    to { transform: rotate(360deg); }
}

/* Responsive calendar */
@media (max-width: 768px) {
    .calendar-day {
        min-height: 60px;
        padding: 0.25rem;
    }

    .calendar-day-header {
        padding: 0.5rem 0.25rem;
        font-size: 0.75rem;
    }

    .calendar-day-number {
        font-size: 0.75rem;
    }

    .calendar-reservations {
        display: none;
    }
}

/* Calendar animations */
.calendar-fade-in {
    animation: calendarFadeIn 0.3s ease-out;
}

@keyframes calendarFadeIn {
    from {
        opacity: 0;
        transform: translateY(10px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}
</style>
@endpush

@push('scripts')
<script>
class InteractiveCalendar {
    constructor() {
        this.currentDate = new Date();
        this.selectedDate = null;
        this.reservationsData = {};
        this.monthNames = [
            'Janvier', 'Février', 'Mars', 'Avril', 'Mai', 'Juin',
            'Juillet', 'Août', 'Septembre', 'Octobre', 'Novembre', 'Décembre'
        ];
        this.dayNames = ['Dim', 'Lun', 'Mar', 'Mer', 'Jeu', 'Ven', 'Sam'];

        this.init();
    }

    init() {
        this.bindEvents();
        this.renderCalendar();
        this.loadReservationsData();
    }

    bindEvents() {
        document.getElementById('prevMonth').addEventListener('click', () => {
            this.currentDate.setMonth(this.currentDate.getMonth() - 1);
            this.renderCalendar();
            this.loadReservationsData();
        });

        document.getElementById('nextMonth').addEventListener('click', () => {
            this.currentDate.setMonth(this.currentDate.getMonth() + 1);
            this.renderCalendar();
            this.loadReservationsData();
        });

        document.getElementById('todayBtn').addEventListener('click', () => {
            this.currentDate = new Date();
            this.renderCalendar();
            this.loadReservationsData();
        });
    }

    renderCalendar() {
        const year = this.currentDate.getFullYear();
        const month = this.currentDate.getMonth();

        // Update month display
        document.getElementById('currentMonth').textContent =
            `${this.monthNames[month]} ${year}`;

        // Get first day of month and number of days
        const firstDay = new Date(year, month, 1);
        const lastDay = new Date(year, month + 1, 0);
        const daysInMonth = lastDay.getDate();
        const startingDayOfWeek = firstDay.getDay();

        // Create calendar HTML
        let calendarHTML = '<div class="calendar-header">';

        // Day headers
        this.dayNames.forEach(day => {
            calendarHTML += `<div class="calendar-day-header">${day}</div>`;
        });

        calendarHTML += '</div><div class="calendar-grid">';

        // Previous month's trailing days
        const prevMonth = new Date(year, month - 1, 0);
        const prevMonthDays = prevMonth.getDate();

        for (let i = startingDayOfWeek - 1; i >= 0; i--) {
            const day = prevMonthDays - i;
            calendarHTML += `
                <div class="calendar-day other-month" data-date="${year}-${String(month).padStart(2, '0')}-${String(day).padStart(2, '0')}">
                    <div class="calendar-day-number">${day}</div>
                </div>
            `;
        }

        // Current month days
        const today = new Date();
        for (let day = 1; day <= daysInMonth; day++) {
            const currentDay = new Date(year, month, day);
            const dateString = `${year}-${String(month + 1).padStart(2, '0')}-${String(day).padStart(2, '0')}`;

            let classes = ['calendar-day'];

            // Check if it's today
            if (currentDay.toDateString() === today.toDateString()) {
                classes.push('today');
            }

            // Check if it's in the past
            if (currentDay < today && currentDay.toDateString() !== today.toDateString()) {
                classes.push('past');
            }

            calendarHTML += `
                <div class="${classes.join(' ')}" data-date="${dateString}" onclick="calendar.selectDate('${dateString}')">
                    <div class="calendar-day-number">${day}</div>
                    <div class="calendar-reservations" id="reservations-${dateString}"></div>
                    <div class="calendar-day-status" id="status-${dateString}"></div>
                </div>
            `;
        }

        // Next month's leading days
        const remainingCells = 42 - (startingDayOfWeek + daysInMonth);
        for (let day = 1; day <= remainingCells && remainingCells < 7; day++) {
            calendarHTML += `
                <div class="calendar-day other-month" data-date="${year}-${String(month + 2).padStart(2, '0')}-${String(day).padStart(2, '0')}">
                    <div class="calendar-day-number">${day}</div>
                </div>
            `;
        }

        calendarHTML += '</div>';

        document.getElementById('calendar').innerHTML = calendarHTML;
        document.getElementById('calendar').classList.add('calendar-fade-in');
    }

    async loadReservationsData() {
        const year = this.currentDate.getFullYear();
        const month = this.currentDate.getMonth() + 1;

        try {
            // Show loading state
            const loadingHTML = `
                <div class="calendar-loading">
                    <i class="fas fa-spinner fa-spin fa-2x"></i>
                    <span class="ms-2">Chargement des données...</span>
                </div>
            `;
            document.getElementById('calendar').innerHTML = loadingHTML;

            // Fetch reservations data for the month
            const response = await fetch(`/api/calendar/reservations?year=${year}&month=${month}`);
            const data = await response.json();

            this.reservationsData = data.reservations || {};

            // Re-render calendar with data
            this.renderCalendar();
            this.updateCalendarWithReservations();

        } catch (error) {
            console.error('Error loading reservations data:', error);
            // Render calendar anyway with empty data
            this.renderCalendar();
        }
    }

    updateCalendarWithReservations() {
        Object.keys(this.reservationsData).forEach(date => {
            const reservations = this.reservationsData[date];
            const reservationsElement = document.getElementById(`reservations-${date}`);
            const statusElement = document.getElementById(`status-${date}`);

            if (reservationsElement && statusElement) {
                // Update reservations count
                if (reservations.length > 0) {
                    reservationsElement.textContent = `${reservations.length} rés.`;
                }

                // Update status indicator
                const totalLocals = {{ $stats['total_locals'] }}; // Total available locals
                const reservedCount = reservations.length;

                if (reservedCount === 0) {
                    statusElement.className = 'calendar-day-status';
                } else if (reservedCount < totalLocals * 0.5) {
                    statusElement.className = 'calendar-day-status partial';
                } else {
                    statusElement.className = 'calendar-day-status full';
                }
            }
        });
    }

    selectDate(dateString) {
        // Remove previous selection
        document.querySelectorAll('.calendar-day.selected').forEach(el => {
            el.classList.remove('selected');
        });

        // Add selection to clicked date
        const dayElement = document.querySelector(`[data-date="${dateString}"]`);
        if (dayElement && !dayElement.classList.contains('past')) {
            dayElement.classList.add('selected');
            this.selectedDate = dateString;
            this.showDateDetails(dateString);
        }
    }

    showDateDetails(dateString) {
        const reservations = this.reservationsData[dateString] || [];
        const date = new Date(dateString);
        const formattedDate = date.toLocaleDateString('fr-FR', {
            weekday: 'long',
            year: 'numeric',
            month: 'long',
            day: 'numeric'
        });

        document.getElementById('selectedDateTitle').textContent = formattedDate;

        let detailsHTML = '';

        if (reservations.length === 0) {
            detailsHTML = `
                <div class="text-center">
                    <i class="fas fa-calendar-plus fa-2x text-success mb-2 d-block"></i>
                    <p class="text-success mb-2">Aucune réservation pour cette date</p>
                    <p class="text-muted small">Tous les locaux sont disponibles</p>
                    <a href="{{ route('locals.index') }}?date=${dateString}" class="btn btn-success btn-sm">
                        <i class="fas fa-plus me-1"></i>Faire une réservation
                    </a>
                </div>
            `;
        } else {
            detailsHTML = `
                <div class="mb-3">
                    <div class="d-flex justify-content-between align-items-center mb-2">
                        <span class="text-muted small">Réservations du jour</span>
                        <span class="badge bg-primary">${reservations.length}</span>
                    </div>
                </div>
            `;

            reservations.forEach(reservation => {
                const statusClass = reservation.status === 'confirmée' ? 'confirmed' :
                                  reservation.status === 'en attente' ? 'pending' : 'cancelled';

                detailsHTML += `
                    <div class="reservation-item ${statusClass}">
                        <div class="d-flex justify-content-between align-items-start mb-1">
                            <strong class="text-truncate">${reservation.local_name}</strong>
                            <span class="badge bg-${statusClass === 'confirmed' ? 'success' : statusClass === 'pending' ? 'warning' : 'danger'} ms-2">
                                ${reservation.status}
                            </span>
                        </div>
                        <div class="text-muted small">
                            <i class="fas fa-clock me-1"></i>
                            ${reservation.start_time} - ${reservation.end_time}
                        </div>
                        <div class="text-muted small">
                            <i class="fas fa-map-marker-alt me-1"></i>
                            ${reservation.location}
                        </div>
                    </div>
                `;
            });

            detailsHTML += `
                <div class="text-center mt-3">
                    <a href="{{ route('locals.index') }}?date=${dateString}" class="btn btn-outline-primary btn-sm">
                        <i class="fas fa-search me-1"></i>Voir les disponibilités
                    </a>
                </div>
            `;
        }

        document.getElementById('selectedDateInfo').innerHTML = detailsHTML;
    }
}

// Initialize calendar when DOM is loaded
document.addEventListener('DOMContentLoaded', function() {
    window.calendar = new InteractiveCalendar();
});
</script>
@endpush